import React from "react";
import avatar from "assets/img/avatars/avatar4.png";

const Header = ({ empData }) => {
  console.log("Employer Header empData:", empData);
  return (
    <div className="col-span-full flex items-center space-x-4 lg:col-span-3">
      <img
        src={
          empData?.profile_picture
            ? empData.profile_picture.startsWith("http")
                ? empData.profile_picture
                : `${import.meta.env.VITE_APP_HOST}/${empData.profile_picture}`
            : avatar
        }
        alt="User Avatar"
        className="h-16 w-16 rounded-full object-cover shadow-md"
        onError={(e) => {
          if (e.target.src !== avatar) {
            e.target.onerror = null;
            e.target.src = avatar;
          }
        }}
      />
      <div className="flex flex-col space-y-1">
        <h3 className="text-xl font-semibold text-gray-800 dark:text-white md:text-2xl">
          {empData?.name || "Default User"}
        </h3>
        <div>
          <span className="w-max rounded-full bg-brand-500 px-2 py-1 text-xs font-medium text-white shadow md:px-3 md:font-semibold">
            {empData?.plan_name || "PRO"} Plan
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500 dark:text-gray-300">
            Credits:
          </span>
          <span className="text-sm font-bold text-brand-500 md:text-lg">
            {empData?.creditsLeft || 0}
          </span>
        </div>
      </div>
    </div>
  );
};

export default Header;
