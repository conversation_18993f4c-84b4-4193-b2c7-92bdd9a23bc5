import React from "react";
import StatCard from "../components/StatCard";

const StatsOverview = ({
  shortListedCandidatesCount,
  unlockedCandidatesCount,
  InterviewedCandidatesCount,
  offeredCandidatesCount,
  navigate,
}) => (
  <div className="flex-wrap justify-center  md:flex-nowrap gap-8 md:gap-0  col-span-full flex items-center md:space-x-4 lg:col-span-5">
    <StatCard
      title="Shortlisted"
      value={shortListedCandidatesCount}
      gradientClass="bg-gradient-to-r from-blue-500 to-blue-700"
      onClick={() => navigate("/employer/track-candidates?tab=Shortlisted")}
    />
    <StatCard
      title="Unlocked"
      value={unlockedCandidatesCount}
      gradientClass="bg-gradient-to-r from-yellow-500 to-yellow-700"
      onClick={() => navigate("/employer/track-candidates?tab=Unlocked")}
    />
    <StatCard
      title="Interviews"
      value={InterviewedCandidatesCount}
      gradientClass="bg-gradient-to-r from-orange-500 to-orange-700"
      onClick={() => navigate("/employer/track-candidates?tab=Interview")}
    />
    <StatCard
      title="Offers"
      value={offeredCandidatesCount}
      gradientClass="bg-gradient-to-r from-green-500 to-green-700"
      onClick={() => navigate("/employer/track-candidates?tab=Offers")}
    />
  </div>
);

export default StatsOverview;