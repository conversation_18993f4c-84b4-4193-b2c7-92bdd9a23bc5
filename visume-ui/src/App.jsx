import React, { useEffect } from "react";
import {
  Routes,
  Route,
  Navigate,
  useNavigate,
  useLocation,
} from "react-router-dom";

// import RtlLayout from "layouts/rtl";
import CandidateLayout from "layouts/candidate";
import AdminLayout from "layouts/admin";
import AuthLayout from "layouts/auth";
import EmployerLayout from "layouts/employer";
import CreateVideoProfile from "views/candidate/CreateVideoProfile";
import DeviceTest from "views/candidate/DeviceTest/DeviceTest";
import NotFound from "views/NotFound";
import BusinessAssociate from "views/partner/BusinessAssociate";
import ProfileSearch from "views/employer/ProfileSearch";
import ViewVideoProfile from "views/candidate/components/ViewVideoProfile";
import CandidateProfile from "views/employer/CandidateProfile";
import JSOnboarding from "views/auth/JSOnboarding";
import CreateAccount from "views/auth/CreateAccount/CreateAccount";
import Cookies from "js-cookie";
import { Toaster } from "react-hot-toast";
import EmpCreateAccount from "views/employer/EmpCreateAccount";
import SignIn from "views/auth/SignIn";
import EmpSignIn from "views/auth/EmpSignIn";
import AdminSignin from "views/auth/AdminSignin";
import Admin from "./layouts/admin/index";
import CandidateProfiles from "views/Admin/CandidateProfiles";
import EmployerProfiles from "views/Admin/EmployerProfiles";
import CandidatePlans from "views/Admin/CandidatePlans";
import VideoProfiles from "views/Admin/VideoProfiles";
import ViewResume from "views/ViewResume";

const App = () => {
  const jstoken = Cookies.get("jstoken");
  const role = Cookies.get("role");
  const navigate = useNavigate();
  const location = useLocation(); // currentPath = /candidate/signIn

  useEffect(() => {
    const isAuthRoute = // isAuthRoute = False
      location.pathname.startsWith("/create-account") ||
      location.pathname.startsWith("/auth") ||
      location.pathname.startsWith("/ba/signin") ||
      location.pathname.startsWith("/admin/signin");

    const candId = Cookies.get("candId") || Cookies.get("empId"); // Use a fallback
    //console.log(candId)

    if (!jstoken || !candId || !role) {
      if (!isAuthRoute) {
        navigate("/candidate/signIn");
      }
    }
  }, []);

  return (
    <>
      <div key={location.pathname}>
        <Routes>
          {/* <Route path="admin/*" element={role === "admin" ? <AdminLayout /> : <NotFound />} /> */}
          <Route path="admin/*" element={<AdminLayout />} />
          <Route
            path="employer/*"
            element={role === "employer" ? <EmployerLayout /> : <NotFound />}
          />
          <Route
            path="candidate/*"
            element={role === "jobseeker" ? <CandidateLayout /> : <NotFound />}
          />
          <Route
            path="profile-search/*"
            element={role === "employer" ? <ProfileSearch /> : <NotFound />}
          />
          {/* <Route path="admin/candidateProfiles" element={<CandidateProfiles />}/> */}
          <Route
            path="profile/:vpid"
            element={role === "employer" ? <CandidateProfile /> : <NotFound />}
          />
          <Route
            path="employer/candidate-profile/:vpid"
            element={role === "employer" ? <CandidateProfile /> : <NotFound />}
          />
          <Route
            path="/candidate/videoResume/:id"
            element={<ViewVideoProfile />}
          />
          <Route path="/create-account/candidate" element={<CreateAccount />} />
          <Route path="/create-account/employer" element={<EmpCreateAccount />} />
          <Route
            path="/candidate/videoResume/"
            element={<Navigate to="/candidate/dashboard" replace />}
          />
          <Route
            path="/create-video-resume/:videoResumeId"
            element={<CreateVideoProfile />}
          />
          <Route
            path="/create-video-resume/"
            element={<Navigate to="/candidate/dashboard" replace />}
          />
          <Route
            path="/candidate/interview/:vpid"
            element={role === "jobseeker" ? <DeviceTest /> : <NotFound />}
          />
          <Route
            path="/partner/BusinessAssociate"
            element={<BusinessAssociate />}
          />
          <Route
            path="/"
            element={
              <Navigate
                to={
                  jstoken
                    ? role === "jobseeker"
                      ? "/candidate/dashboard"
                      : role === "employer"
                      ? "/employer/dashboard"
                      : "/candidate/signIn"
                    : "/candidate/signIn"
                }
                replace
              />
            }
          />
          <Route path="*" element={<NotFound />} />
          <Route path="/candidate/onboarding" element={<JSOnboarding />} />
          <Route path="/candidate/signIn" element={<SignIn />} />
          <Route path="/employer/signIn" element={<EmpSignIn />} />

          <Route path="/admin/signIn" element={<AdminSignin />} />

          <Route path="/resume" element={<ViewResume />} />
        </Routes>
      </div>
      <Toaster position="top-center" toastOptions={{ duration: 2000 }} />
    </>
  );
};

export default App;
