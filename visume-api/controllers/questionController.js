const { generateSingleQuestion } = require("../utils/helpers");
// Phase 2: Re-add requirement imports for one-to-one question-requirement mapping
const {
  getRequirementsForProfile
  // Note: Not importing assessment functions as we don't evaluate during interview
} = require("../utils/requirementsAssessment");

exports.generateNextQuestion = async (req, res) => {
  const MIN_QUESTIONS = 5;
  
  try {
    const { role, skills, previousQuestions, companyType, experience, videoProfileId } = req.body;

    // Validate required fields
    if (!role) {
      return res.status(400).json({
        success: false,
        message: "Role is required for question generation"
      });
    }

    // Parse and validate previous questions array
    // Frontend sends previousQuestions as an array of objects, each containing question and answer
    const previousQA = (previousQuestions || []).map((q) => ({
      question: q.question,
      answer: q.answer || null, // Directly use the answer from the question object
      type: q.type,
      question_number: q.question_number,
      total_questions: q.total_questions
    }));

    console.log("Previous Questions:", previousQA);

    // Phase 2: Fetch requirements to determine interview length (N requirements = N questions)
    let totalQuestions = 10; // Default maximum
    let requirements = null;
    let currentRequirement = null;

    if (videoProfileId) {
      try {
        console.log(`🔍 FETCHING REQUIREMENTS for interview length calculation: ${videoProfileId}`);
        requirements = await getRequirementsForProfile(videoProfileId);

        if (requirements && requirements.requirements) {
          // Phase 2: Set interview length equal to number of requirements (max 10)
          totalQuestions = Math.min(requirements.requirements.length, 10);
          console.log(`📊 INTERVIEW LENGTH SET: ${totalQuestions} questions for ${requirements.requirements.length} requirements`);

          // Get the current requirement for this question (sequential mapping)
          const currentQuestionNumber = (previousQuestions || []).length + 1;
          const requirementIndex = currentQuestionNumber - 1; // 0-based index

          if (requirementIndex < requirements.requirements.length) {
            currentRequirement = requirements.requirements[requirementIndex];
            console.log(`🎯 CURRENT REQUIREMENT: Question ${currentQuestionNumber} targets requirement ${requirementIndex + 1}: ${currentRequirement.parameter || currentRequirement.description}`);
          } else {
            console.log(`⚠️ WARNING: Question ${currentQuestionNumber} has no corresponding requirement (requirementIndex: ${requirementIndex}, total requirements: ${requirements.requirements.length})`);
          }
        } else {
          console.log(`⚠️ NO REQUIREMENTS FOUND: Using default totalQuestions = ${totalQuestions}`);
        }
      } catch (reqError) {
        console.error("❌ Failed to fetch requirements, using default interview length:", reqError);
        console.log(`🔧 FALLBACK: Using default totalQuestions = ${totalQuestions}`);
        // Continue with default length if requirements fetch fails
      }
    } else {
      console.log(`⚠️ NO VIDEO PROFILE ID: Using default totalQuestions = ${totalQuestions}`);
    }

    const questionCount = (previousQuestions || []).length + 1;

    // Phase 3: Log interview progress for debugging
    console.log(`📊 INTERVIEW PROGRESS: Question ${questionCount} of ${totalQuestions} (${requirements?.requirements?.length || 'N/A'} requirements)`);

    // Phase 3: Simple count-based interview termination - interview ends after exactly N questions
    if (questionCount > totalQuestions) {
      console.log(`🏁 INTERVIEW COMPLETED: Attempting to generate question ${questionCount} but max is ${totalQuestions} (${questionCount - 1} completed)`);
      return res.status(200).json({
        success: false,
        message: "Interview completed",
        completed: true,
        totalQuestionsCompleted: questionCount - 1,
        maxQuestions: totalQuestions
      });
    }

    console.log(`Generating question ${questionCount} of ${totalQuestions}`);

    // Phase 2: One-to-one question-requirement mapping without real-time evaluation

    // Phase 2: Question type determination with requirement context
    const isFirstQuestion = !previousQuestions || previousQuestions.length === 0;
    let forcedType = null;

    if (isFirstQuestion) {
      forcedType = "behavioral";
      console.log(`🎬 FIRST QUESTION: Starting with behavioral question`);
    } else if (currentRequirement) {
      // Phase 2: Use requirement category to guide question type while maintaining alternation
      const prevTypes = previousQA.map(q => q.type);
      const lastType = prevTypes[prevTypes.length - 1];
      const category = currentRequirement.category?.toLowerCase() || '';

      // Determine preferred type based on requirement category
      let preferredType = null;
      if (category.includes('technical') || category.includes('problem_solving') || category.includes('coding')) {
        preferredType = "technical";
      } else if (category.includes('communication') || category.includes('behavioral') || category.includes('leadership')) {
        preferredType = "behavioral";
      }

      // Use preferred type if available, otherwise alternate
      if (preferredType) {
        forcedType = preferredType;
        console.log(`🎯 REQUIREMENT-GUIDED TYPE: ${forcedType} for ${category} requirement`);
      } else {
        // Fallback to alternation if category doesn't clearly indicate type
        forcedType = lastType === "behavioral" ? "technical" : "behavioral";
        console.log(`🔄 ALTERNATING TYPE: ${forcedType} (previous: ${lastType}) for general requirement`);
      }
    } else {
      // Fallback to simple alternation if no requirement context
      const prevTypes = previousQA.map(q => q.type);
      const lastType = prevTypes[prevTypes.length - 1];
      forcedType = lastType === "behavioral" ? "technical" : "behavioral";
      console.log(`🔄 FALLBACK ALTERNATION: ${forcedType} (previous: ${lastType})`);
    }

    const nextQuestion = await generateSingleQuestion(
      role,
      previousQA,
      skills,
      isFirstQuestion,
      companyType,
      experience,
      forcedType,
      currentRequirement // Phase 2: Pass current requirement for context-aware question generation
    );

    // Validate and ensure timerDuration is within acceptable range
    const timerDuration = Math.min(90, Math.max(30, parseInt(nextQuestion.timerDuration) || 90));

    const response = {
      success: true,
      question: nextQuestion.question,
      type: nextQuestion.type,
      timerDuration: timerDuration,
      question_number: questionCount,
      total_questions: totalQuestions,
      completed: false, // BUG FIX: Never mark as completed when successfully returning a question
                       // The interview should only be marked complete when we refuse to generate more questions
      _fallback: nextQuestion._fallback || false
    };

    res.status(200).json(response);

  } catch (error) {
    console.error("Error generating next question:", error);
    
    // Simple fallback
    const fallbackQuestion = {
      // Remove fallback questions; throw error if no questions can be generated
      question: null,
      type: "behavioral",
      timerDuration: 90, // Default fallback duration
      _fallback: true
    };

    const status = error.message === "Interview completed" ? 200 : 500;
    res.status(status).json({
      success: status === 200,
      ...fallbackQuestion,
      completed: error.message === "Interview completed",
      error_details: error.message
    });
  }
};

exports.analyzeAnswer = async (req, res) => {
  try {
    const { question, answer, role, skills, previousQA, videoProfileId } = req.body;

    if (!question || !role) {
      return res.status(400).json({
        message: "Question and role are required fields"
      });
    }

    // Import helpers
    const { analyzeAnswerAndGenerateFollowUp } = require("../utils/helpers");

    console.log("🔍 ANALYZING ANSWER:", {
      question: question.substring(0, 50) + "...",
      answerLength: answer?.length,
      role,
      skillsCount: skills?.length,
      hasVideoProfileId: !!videoProfileId
    });

    // Phase 1: Removed requirement fetching and assessment to eliminate real-time requirement evaluation
    // Get analysis and follow-up (without requirements for simplified flow)
    const analysisResult = await analyzeAnswerAndGenerateFollowUp(
      question,
      answer,
      role,
      skills,
      previousQA,
      null // Phase 1: Pass null instead of requirements to disable requirement assessment
    );

    // Phase 1: Removed requirement database updates as we're not tracking requirements during interview

    console.log("✅ ANALYSIS COMPLETED:", {
      hasAnalysis: !!analysisResult.analysis,
      hasFollowUp: !!analysisResult.follow_up?.question
      // Phase 1: Removed requirementsStatus logging as we're not tracking requirements during interview
    });

    // Phase 1: Return simplified analysis result without requirements status
    res.json({
      analysis: analysisResult.analysis,
      follow_up: analysisResult.follow_up
      // Phase 1: Removed requirementsStatus from response to eliminate requirement tracking during interview
    });

  } catch (error) {
    console.error("Error analyzing answer:", error);
    res.status(500).json({
      message: "Failed to analyze answer",
      error: error.message,
      analysis: {
        technical_accuracy: "Analysis failed",
        communication: "Analysis failed",
        knowledge_gaps: ["Unable to analyze response"]
      },
      follow_up: {
        question: "Could you provide more details about your previous answer?",
        reasoning: "Default follow-up due to analysis error",
        expected_focus: "General elaboration"
      }
    });
  }
};
